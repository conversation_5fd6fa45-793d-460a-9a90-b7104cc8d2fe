# DiQDiff 多兴趣改进版本

## 概述

本项目实现了对原始DiQDiff模型的重要改进，将其从"单兴趣"量化扩展到"多兴趣"量化，以更好地捕捉用户的多样化兴趣偏好。

## 核心改进

### 1. 多兴趣量化模块 (SVQ-MI)

**原始实现问题：**
- 只能为每个用户序列识别一个主导兴趣
- 无法捕捉用户的多样化兴趣偏好
- 限制了推荐的多样性和准确性

**改进方案：**
- 实现了基于注意力的多兴趣选择机制
- 使用Gumbel-TopK采样从码本中选择K个最相关的兴趣向量
- 保持端到端可微训练

**技术实现：**
```python
class MultiInterestQuantization(nn.Module):
    def __init__(self, args):
        self.attention_net = nn.Sequential(...)  # 注意力网络
        self.gumbel_topk = GumbelTopK(tau=0.1, hard=True)  # 可微TopK采样
    
    def forward(self, input_embeddings):
        # 1. 计算序列与码本的亲和度
        affinity_scores = torch.matmul(enhanced_repr, all_centroids.t())
        
        # 2. 使用Gumbel-TopK选择多个兴趣
        interest_indices, interest_weights = self.gumbel_topk(affinity_scores, self.num_interests)
        
        # 3. 获取对应的码本向量
        selected_centroids = all_centroids[interest_indices]
        
        return selected_centroids, all_centroids, interest_indices
```

### 2. 交叉注意力机制

**核心思想：**
- 将当前去噪状态作为查询(Query)
- 将多个兴趣向量作为键(Key)和值(Value)
- 动态计算注意力权重，选择最相关的兴趣信息

**实现细节：**
```python
class CrossAttentionLayer(nn.Module):
    def forward(self, query, key_value):
        # query: [batch_size, hidden_size] 当前去噪状态
        # key_value: [batch_size, num_interests, hidden_size] 多兴趣向量
        
        Q = self.query_proj(query)
        K = self.key_proj(key_value)
        V = self.value_proj(key_value)
        
        attention_scores = torch.matmul(Q, K.transpose(-2, -1))
        attention_weights = F.softmax(attention_scores, dim=-1)
        context = torch.matmul(attention_weights, V)
        
        return self.layer_norm(context + query)
```

### 3. 自适应去噪网络

**改进的去噪过程：**
```python
def denoise_multi_interest(self, item_rep, x_t, t, mask_seq, multi_interests):
    # 1. 基础历史信息处理
    base_input = item_rep * self.lambda_history + 0.001 * x_t.unsqueeze(1)
    base_res = self.att(base_input, mask_seq)
    current_state = base_res[:, -1, :]
    
    # 2. 交叉注意力融合多兴趣
    multi_interest_context = self.cross_attention(current_state, multi_interests)
    
    # 3. 结合多兴趣上下文
    out = current_state + self.lambda_multi_interest * multi_interest_context
    
    return out
```

## 文件结构

```
src/
├── Modules.py                    # 新增多兴趣相关模块
│   ├── GumbelTopK               # 可微TopK采样
│   ├── MultiInterestQuantization # 多兴趣量化模块
│   └── CrossAttentionLayer      # 交叉注意力层
├── codiffu_multi_interest.py    # 多兴趣版本的完整模型
├── codiffu.py                   # 原始模型（已更新支持多兴趣）
├── main.py                      # 主训练脚本（已添加多兴趣参数）
├── test_multi_interest.py       # 完整测试脚本
├── simple_test.py               # 简化测试脚本
└── demo_multi_interest.py       # 演示脚本
```

## 新增超参数

```python
# 多兴趣相关参数
parser.add_argument('--use_multi_interest', type=bool, default=True, 
                   help='Whether to use multi-interest quantization')
parser.add_argument('--num_interests', type=int, default=3, 
                   help='Number of interests to extract from user sequence')
parser.add_argument('--lambda_multi_interest', type=float, default=0.5, 
                   help='Weight for multi-interest context fusion')
```

## 使用方法

### 训练多兴趣模型

```bash
# ML-1M数据集
python main.py --dataset ml-1m --num_cluster 8 --lambda_intent 0.6 \
               --lambda_history 1 --lambda_contra 0.05 \
               --use_multi_interest True --num_interests 3 \
               --lambda_multi_interest 0.5

# Steam数据集
python main.py --dataset steam --num_cluster 32 --lambda_intent 1.2 \
               --lambda_history 1 --lambda_contra 0.2 \
               --use_multi_interest True --num_interests 4 \
               --lambda_multi_interest 0.6
```

### 向后兼容

```bash
# 使用原始单兴趣模式
python main.py --dataset ml-1m --use_multi_interest False
```

## 预期改进效果

### 1. 更准确的用户建模
- **多维度兴趣捕捉**：能够同时识别用户的多个兴趣领域
- **动态兴趣权重**：根据上下文动态调整不同兴趣的重要性

### 2. 更好的推荐质量
- **准确性提升**：通过多兴趣引导，生成更符合用户偏好的推荐
- **多样性增强**：避免推荐结果过于集中在单一兴趣领域

### 3. 更强的泛化能力
- **冷启动改善**：多兴趣建模有助于处理新用户和新物品
- **长尾物品推荐**：更好地推荐小众但符合用户兴趣的物品

## 技术优势

### 1. 端到端可微
- 使用Gumbel-TopK采样确保整个多兴趣选择过程可微
- 支持端到端训练，无需额外的预训练步骤

### 2. 计算效率
- 交叉注意力机制计算复杂度为O(K)，其中K是兴趣数量
- 相比全注意力机制，计算开销较小

### 3. 灵活性
- 支持动态调整兴趣数量
- 可以根据不同数据集特点调整超参数

## 实验建议

### 1. 超参数调优
- `num_interests`: 建议从2-5开始尝试
- `lambda_multi_interest`: 建议从0.3-0.7范围调整
- 根据数据集特点调整兴趣数量

### 2. 评估指标
- **准确性**: HR@K, NDCG@K
- **多样性**: ILD (Intra-List Diversity)
- **覆盖率**: Catalog Coverage
- **新颖性**: Novelty Score

### 3. 消融实验
- 比较单兴趣 vs 多兴趣效果
- 分析不同兴趣数量的影响
- 研究交叉注意力权重的分布

## 未来改进方向

1. **层次化兴趣建模**：考虑兴趣之间的层次关系
2. **时序兴趣演化**：建模用户兴趣随时间的变化
3. **个性化兴趣数量**：为不同用户自适应确定兴趣数量
4. **跨域兴趣迁移**：利用多兴趣进行跨域推荐

## 总结

本改进将DiQDiff从单兴趣扩展到多兴趣，通过多兴趣量化模块和交叉注意力机制，实现了更精准和多样化的推荐。改进保持了原模型的优势，同时显著提升了对用户复杂兴趣偏好的建模能力。
