#!/usr/bin/env python3
"""
Dropout配置文件
提供不同强度的正则化配置来解决过拟合问题
"""

def get_dropout_config(level='standard'):
    """
    获取不同强度的dropout配置
    
    Args:
        level: 'conservative', 'standard', 'aggressive'
    
    Returns:
        dict: dropout配置参数
    """
    
    configs = {
        'conservative': {
            'description': '保守的dropout设置，适合数据量大、模型稳定的情况',
            'dropout': 0.1,              # 基础dropout
            'emb_dropout': 0.3,          # 嵌入dropout（保持原有）
            'attention_dropout': 0.15,   # 注意力网络dropout
            'interest_dropout': 0.1,     # 兴趣计算dropout
            'fusion_dropout': 0.1,       # 信息融合dropout
            'use_case': '数据量充足，模型训练稳定'
        },
        
        'standard': {
            'description': '标准的dropout设置，平衡性能和正则化',
            'dropout': 0.1,              # 基础dropout
            'emb_dropout': 0.3,          # 嵌入dropout（保持原有）
            'attention_dropout': 0.2,    # 注意力网络dropout
            'interest_dropout': 0.15,    # 兴趣计算dropout
            'fusion_dropout': 0.15,      # 信息融合dropout
            'use_case': '大多数情况下的推荐设置'
        },
        
        'aggressive': {
            'description': '激进的dropout设置，适合严重过拟合的情况',
            'dropout': 0.15,             # 基础dropout（提升）
            'emb_dropout': 0.4,          # 嵌入dropout（提升）
            'attention_dropout': 0.3,    # 注意力网络dropout
            'interest_dropout': 0.25,    # 兴趣计算dropout
            'fusion_dropout': 0.2,       # 信息融合dropout
            'use_case': '严重过拟合，需要强力正则化'
        }
    }
    
    if level not in configs:
        raise ValueError(f"Unknown dropout level: {level}. Choose from {list(configs.keys())}")
    
    return configs[level]

def get_dataset_specific_config(dataset_name):
    """
    根据数据集特点提供推荐的dropout配置
    
    Args:
        dataset_name: 数据集名称
    
    Returns:
        dict: 推荐的dropout配置
    """
    
    dataset_configs = {
        'ml-1m': {
            'description': 'MovieLens-1M数据集配置',
            'recommended_level': 'standard',
            'custom_adjustments': {
                'attention_dropout': 0.18,  # 电影兴趣相对集中
                'interest_dropout': 0.12,
                'fusion_dropout': 0.12
            },
            'reasoning': '电影推荐中用户兴趣相对稳定，使用中等强度dropout'
        },
        
        'amazon_beauty': {
            'description': 'Amazon Beauty数据集配置',
            'recommended_level': 'standard',
            'custom_adjustments': {
                'attention_dropout': 0.22,  # 美妆品类复杂，需要更强正则化
                'interest_dropout': 0.18,
                'fusion_dropout': 0.16
            },
            'reasoning': '美妆产品类别丰富，用户兴趣多样，需要适度强化dropout'
        },
        
        'steam': {
            'description': 'Steam游戏数据集配置',
            'recommended_level': 'aggressive',
            'custom_adjustments': {
                'attention_dropout': 0.25,  # 游戏类型极其丰富
                'interest_dropout': 0.2,
                'fusion_dropout': 0.18
            },
            'reasoning': '游戏类型极其多样，用户兴趣变化大，需要强力正则化'
        },
        
        'toys': {
            'description': 'Toys数据集配置',
            'recommended_level': 'standard',
            'custom_adjustments': {
                'attention_dropout': 0.2,
                'interest_dropout': 0.15,
                'fusion_dropout': 0.15
            },
            'reasoning': '玩具类别适中，使用标准dropout配置'
        }
    }
    
    if dataset_name not in dataset_configs:
        print(f"Warning: No specific config for dataset '{dataset_name}', using standard config")
        return get_dropout_config('standard')
    
    # 获取基础配置
    base_config = get_dropout_config(dataset_configs[dataset_name]['recommended_level'])
    
    # 应用数据集特定的调整
    custom_adjustments = dataset_configs[dataset_name]['custom_adjustments']
    for key, value in custom_adjustments.items():
        base_config[key] = value
    
    # 添加数据集特定信息
    base_config['dataset_info'] = dataset_configs[dataset_name]
    
    return base_config

def print_config_comparison():
    """打印不同配置的对比"""
    
    print("=" * 80)
    print("Dropout配置对比")
    print("=" * 80)
    
    levels = ['conservative', 'standard', 'aggressive']
    
    for level in levels:
        config = get_dropout_config(level)
        print(f"\n【{level.upper()}】- {config['description']}")
        print(f"适用场景: {config['use_case']}")
        print("-" * 50)
        
        for key, value in config.items():
            if key not in ['description', 'use_case']:
                print(f"{key:20s}: {value}")
    
    print("\n" + "=" * 80)
    print("数据集特定配置推荐")
    print("=" * 80)
    
    datasets = ['ml-1m', 'amazon_beauty', 'steam', 'toys']
    
    for dataset in datasets:
        config = get_dataset_specific_config(dataset)
        print(f"\n【{dataset.upper()}】")
        print(f"推荐理由: {config['dataset_info']['reasoning']}")
        print("-" * 50)
        
        key_params = ['attention_dropout', 'interest_dropout', 'fusion_dropout']
        for key in key_params:
            print(f"{key:20s}: {config[key]}")

def generate_training_command(dataset, dropout_level='standard'):
    """
    生成带有dropout配置的训练命令
    
    Args:
        dataset: 数据集名称
        dropout_level: dropout强度级别
    
    Returns:
        str: 完整的训练命令
    """
    
    # 获取数据集特定配置
    config = get_dataset_specific_config(dataset)
    
    # 基础命令参数
    base_params = {
        'ml-1m': '--num_cluster 8 --lambda_intent 0.6 --lambda_history 1 --lambda_contra 0.05',
        'amazon_beauty': '--num_cluster 32 --lambda_intent 0.4 --lambda_history 0.6 --lambda_contra 1',
        'steam': '--num_cluster 32 --lambda_intent 1.2 --lambda_history 1 --lambda_contra 0.2',
        'toys': '--num_cluster 32 --lambda_intent 0.4 --lambda_history 1 --lambda_contra 1'
    }
    
    base_cmd = f"python main.py --dataset {dataset} {base_params.get(dataset, '')}"
    
    # 添加dropout参数
    dropout_params = [
        f"--dropout {config['dropout']}",
        f"--emb_dropout {config['emb_dropout']}",
        f"--attention_dropout {config['attention_dropout']}",
        f"--interest_dropout {config['interest_dropout']}",
        f"--fusion_dropout {config['fusion_dropout']}",
        "--use_multi_interest True",
        "--num_interests 3",
        "--lambda_multi_interest 0.5",
        "--eval_interval 2",
        "--patience 10"
    ]
    
    full_cmd = base_cmd + " " + " ".join(dropout_params)
    
    return full_cmd

def main():
    """主函数：展示配置和生成命令"""
    
    print_config_comparison()
    
    print("\n" + "=" * 80)
    print("推荐的训练命令")
    print("=" * 80)
    
    datasets = ['amazon_beauty', 'ml-1m', 'steam']
    
    for dataset in datasets:
        print(f"\n【{dataset.upper()}】:")
        cmd = generate_training_command(dataset)
        print(cmd)
        print()

if __name__ == "__main__":
    main()
