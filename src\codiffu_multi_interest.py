import torch.nn as nn
import torch as th
from step_sample import create_named_schedule_sampler
import numpy as np
import math
import torch
import torch.nn.functional as F
from Modules import *


class CoDiffuMultiInterest(nn.Module):
    """
    DiQDiff的多兴趣改进版本
    主要改进：
    1. 多兴趣量化模块 (SVQ-MI)
    2. 交叉注意力机制融合多兴趣
    3. 动态兴趣引导的去噪过程
    """
    def __init__(self, args):
        super(CoDiffuMultiInterest, self).__init__()
        self.hidden_size = args.hidden_size
        self.schedule_sampler_name = args.schedule_sampler_name
        self.diffusion_steps = args.diffusion_steps

        # 扩散过程参数设置
        self.noise_schedule = args.noise_schedule
        betas = self.get_betas(self.noise_schedule, self.diffusion_steps)
        betas = np.array(betas, dtype=np.float64)
        self.betas = betas
        assert len(betas.shape) == 1, "betas must be 1-D"
        assert (betas > 0).all() and (betas <= 1).all()
        
        alphas = 1.0 - betas
        self.alphas_cumprod = np.cumprod(alphas, axis=0)
        self.alphas_cumprod_prev = np.append(1.0, self.alphas_cumprod[:-1])

        # 扩散过程相关计算
        self.sqrt_alphas_cumprod = np.sqrt(self.alphas_cumprod)
        self.sqrt_one_minus_alphas_cumprod = np.sqrt(1.0 - self.alphas_cumprod)
        self.log_one_minus_alphas_cumprod = np.log(1.0 - self.alphas_cumprod)
        self.sqrt_recip_alphas_cumprod = np.sqrt(1.0 / self.alphas_cumprod)
        self.sqrt_recipm1_alphas_cumprod = np.sqrt(1.0 / self.alphas_cumprod - 1)

        self.posterior_mean_coef1 = (betas * np.sqrt(self.alphas_cumprod_prev) / (1.0 - self.alphas_cumprod))
        self.posterior_mean_coef2 = ((1.0 - self.alphas_cumprod_prev) * np.sqrt(alphas) / (1.0 - self.alphas_cumprod))
        self.posterior_variance = (betas * (1.0 - self.alphas_cumprod_prev) / (1.0 - self.alphas_cumprod))

        self.num_timesteps = int(self.betas.shape[0])
        self.schedule_sampler = create_named_schedule_sampler(self.schedule_sampler_name, self.num_timesteps)
        self.rescale_timesteps = args.rescale_timesteps

        # 模型组件
        self.time_embed = nn.Sequential(
            nn.Linear(self.hidden_size, self.hidden_size * 4), 
            SiLU(), 
            nn.Linear(self.hidden_size * 4, self.hidden_size)
        )
        self.att = Transformer_rep(args)
        
        # 权重参数
        self.lambda_history = args.lambda_history
        self.lambda_intent = args.lambda_intent
        self.lambda_multi_interest = getattr(args, 'lambda_multi_interest', 0.5)
        
        # 基础组件
        self.dropout = nn.Dropout(args.dropout)
        self.norm_diffu_rep = LayerNorm(self.hidden_size)
        self.item_num = args.item_num + 1
        self.item_embeddings = nn.Embedding(self.item_num, self.hidden_size)
        self.embed_dropout = nn.Dropout(args.emb_dropout)
        self.LayerNorm = LayerNorm(args.hidden_size, eps=1e-12)
        self.loss_ce = nn.CrossEntropyLoss()

        # 多兴趣相关组件
        self.num_interests = getattr(args, 'num_interests', 3)
        self.n_clusters = args.num_cluster
        self.multi_interest_quantizer = MultiInterestQuantization(args)
        self.cross_attention = CrossAttentionLayer(self.hidden_size, num_heads=4, dropout=args.dropout)
        
        # 向后兼容的单兴趣组件
        self.mlp = nn.Sequential(nn.Linear(self.hidden_size * args.max_len, self.n_clusters))
        self.use_multi_interest = getattr(args, 'use_multi_interest', True)

    def get_betas(self, noise_schedule, diffusion_steps):
        """获取beta调度"""
        betas = get_named_beta_schedule(noise_schedule, diffusion_steps)
        return betas

    def timestep_embedding(self, timesteps, dim, max_period=10000):
        """创建正弦时间步嵌入"""
        half = dim // 2
        freqs = th.exp(-math.log(max_period) * th.arange(start=0, end=half, dtype=th.float32) / half).to(device=timesteps.device)
        args = timesteps[:, None].float() * freqs[None]
        embedding = th.cat([th.cos(args), th.sin(args)], dim=-1)
        if dim % 2:
            embedding = th.cat([embedding, th.zeros_like(embedding[:, :1])], dim=-1)
        return embedding

    def q_sample(self, x_start, t, noise=None, mask=None):
        """前向扩散过程：向数据添加噪声"""
        if noise is None:
            noise = th.randn_like(x_start)

        assert noise.shape == x_start.shape
        x_t = (
            extract_into_tensor(self.sqrt_alphas_cumprod, t, x_start.shape) * x_start
            + extract_into_tensor(self.sqrt_one_minus_alphas_cumprod, t, x_start.shape) * noise
        )

        if mask is None:
            return x_t
        else:
            mask = th.broadcast_to(mask.unsqueeze(dim=-1), x_start.shape)
            return th.where(mask == 0, x_start, x_t)

    def _scale_timesteps(self, t):
        """缩放时间步"""
        if self.rescale_timesteps:
            return t.float() * (1000.0 / self.num_timesteps)
        return t

    def q_posterior_mean_variance(self, x_start, x_t, t):
        """计算扩散后验的均值和方差"""
        assert x_start.shape == x_t.shape
        posterior_mean = (
            extract_into_tensor(self.posterior_mean_coef1, t, x_t.shape) * x_start
            + extract_into_tensor(self.posterior_mean_coef2, t, x_t.shape) * x_t
        )
        assert (posterior_mean.shape[0] == x_start.shape[0])
        return posterior_mean

    def intent_cluster(self, input, n_clusters):
        """原始的单兴趣聚类方法（向后兼容）"""
        X = input.view(input.shape[0], -1)
        centers = X[torch.randperm(X.size(0))[:n_clusters]].to(input.device)
        labels = self.mlp(X)
        labels = F.gumbel_softmax(labels, tau=0.1, hard=True)

        for i in range(n_clusters):
            if torch.sum(labels[:, i]) == 0:
                centers[i] = X[torch.randint(0, X.size(0), (1,))]
            else:
                centers[i] = torch.mean(X[labels[:, i].bool()], dim=0)
        return centers.view(n_clusters, input.shape[1], input.shape[-1]), torch.argmax(labels, dim=1)

    def denoise_multi_interest(self, item_rep, x_t, t, mask_seq, multi_interests):
        """
        多兴趣引导的去噪函数
        Args:
            item_rep: [batch_size, seq_len, hidden_size] 历史序列表示
            x_t: [batch_size, hidden_size] 当前噪声状态
            t: 时间步
            mask_seq: 序列掩码
            multi_interests: [batch_size, num_interests, hidden_size] 多兴趣向量
        """
        emb_t = self.time_embed(self.timestep_embedding(t, self.hidden_size))
        x_t = x_t + emb_t
        
        # 1. 基础的历史信息和噪声信息处理
        base_input = item_rep * self.lambda_history + 0.001 * x_t.unsqueeze(1)
        
        # 2. 通过Transformer处理基础输入
        base_res = self.att(base_input, mask_seq)
        base_res = self.norm_diffu_rep(self.dropout(base_res))
        current_state = base_res[:, -1, :]  # [batch_size, hidden_size]
        
        # 3. 使用交叉注意力融合多兴趣信息
        multi_interest_context = self.cross_attention(current_state, multi_interests)
        
        # 4. 结合多兴趣上下文
        out = current_state + self.lambda_multi_interest * multi_interest_context
        
        return out

    def denoise_single_interest(self, item_rep, x_t, t, mask_seq):
        """原始的单兴趣去噪函数（向后兼容）"""
        emb_t = self.time_embed(self.timestep_embedding(t, self.hidden_size))
        x_t = x_t + emb_t
        res = self.att(item_rep * self.lambda_history + 0.001 * x_t.unsqueeze(1) + 
                      self.centroids[self.labels] * self.lambda_intent, mask_seq)
        res = self.norm_diffu_rep(self.dropout(res))
        out = res[:, -1, :]
        return out

    def denoise(self, item_rep, x_t, t, mask_seq, multi_interests=None):
        """统一的去噪接口"""
        if self.use_multi_interest and multi_interests is not None:
            return self.denoise_multi_interest(item_rep, x_t, t, mask_seq, multi_interests)
        else:
            return self.denoise_single_interest(item_rep, x_t, t, mask_seq)

    def p_mean_variance(self, rep_item, x_t, t, mask_seq, multi_interests=None):
        """计算去噪过程的均值和方差"""
        model_output = self.denoise(rep_item, x_t, self._scale_timesteps(t), mask_seq, multi_interests)

        x_0 = model_output
        model_log_variance = extract_into_tensor(self.posterior_variance, t, x_t.shape)
        model_mean = self.q_posterior_mean_variance(x_start=x_0, x_t=x_t, t=t)
        return model_mean, model_log_variance

    def p_sample(self, item_rep, noise_x_t, t, mask_seq, multi_interests=None):
        """从去噪分布中采样"""
        model_mean, model_log_variance = self.p_mean_variance(item_rep, noise_x_t, t, mask_seq, multi_interests)
        noise = th.randn_like(noise_x_t)
        nonzero_mask = ((t != 0).float().view(-1, *([1] * (len(noise_x_t.shape) - 1))))
        sample_xt = model_mean + nonzero_mask * th.exp(0.5 * model_log_variance) * noise
        return sample_xt

    def reverse_p_sample(self, item_rep, noise_x_t, mask_seq, multi_interests=None):
        """反向扩散采样过程"""
        device = item_rep.device
        indices = list(range(self.num_timesteps))[::-1]

        for i in indices:
            t = th.tensor([i] * item_rep.shape[0], device=device)
            with th.no_grad():
                noise_x_t = self.p_sample(item_rep, noise_x_t, t, mask_seq, multi_interests)
        return noise_x_t

    def diffu(self, item_rep, item_tag, mask_seq, multi_interests=None):
        """扩散训练过程"""
        noise = th.randn_like(item_tag)
        t, weights = self.schedule_sampler.sample(item_rep.shape[0], item_tag.device)
        x_t = self.q_sample(item_tag, t, noise=noise)
        x_0 = self.denoise(item_rep, x_t, self._scale_timesteps(t), mask_seq, multi_interests)
        return x_0

    def loss_diffu_ce(self, rep_diffu, labels):
        """计算扩散损失和对比损失"""
        scores = torch.matmul(rep_diffu, self.item_embeddings.weight.t())
        contra = self.contra_loss(rep_diffu)
        return self.loss_ce(scores, labels.squeeze(-1)), contra

    def diffu_rep_pre(self, rep_diffu):
        """预测阶段的表示计算"""
        scores = torch.matmul(rep_diffu, self.item_embeddings.weight.t())
        return scores

    def contra_loss(self, diffu):
        """对比学习损失"""
        temperature = 0.07
        similarities = (F.cosine_similarity(diffu.unsqueeze(1), diffu.unsqueeze(0), dim=2) / temperature).to(diffu.device)

        # 正样本对
        positives = torch.diag(similarities).to(diffu.device)

        # 负样本对
        mask = torch.eye(len(diffu)).bool().to(diffu.device)
        mask = ~mask.to(diffu.device)
        negatives = similarities[mask].view(len(diffu), -1).to(diffu.device)

        # 计算NCE损失
        logits = torch.cat([positives.unsqueeze(1), negatives], dim=1)
        labels = torch.zeros(len(diffu), dtype=torch.long).to(diffu.device)
        loss = F.cross_entropy(logits, labels)
        return loss

    def forward(self, sequence, tag, train_flag):
        """
        前向传播函数
        Args:
            sequence: [batch_size, seq_len] 用户序列
            tag: [batch_size, 1] 目标物品
            train_flag: 是否为训练模式
        Returns:
            rep_diffu: 扩散表示
            centroids: 聚类中心（用于兼容性）
        """
        # 1. 物品嵌入和预处理
        item_embeddings = self.item_embeddings(sequence)
        item_embeddings = self.embed_dropout(item_embeddings)
        item_embeddings = self.LayerNorm(item_embeddings)

        mask_seq = (sequence > 0).float()

        # 2. 多兴趣量化或单兴趣聚类
        if self.use_multi_interest:
            # 使用多兴趣量化模块
            multi_interests, all_centroids, interest_indices = self.multi_interest_quantizer(item_embeddings)
            # 保存用于损失计算和兼容性
            self.centroids = all_centroids
            self.labels = interest_indices
            self.multi_interests = multi_interests
        else:
            # 原始的单兴趣聚类（向后兼容）
            self.centroids, self.labels = self.intent_cluster(item_embeddings, self.n_clusters)
            multi_interests = None

        # 3. 扩散过程
        if train_flag:
            tag_emb = self.item_embeddings(tag.squeeze(-1))
            rep_diffu = self.diffu(item_embeddings, tag_emb, mask_seq, multi_interests)
        else:
            noise_x_t = th.randn_like(item_embeddings[:, -1, :])
            rep_diffu = self.reverse_p_sample(item_embeddings, noise_x_t, mask_seq, multi_interests)

        return rep_diffu, self.centroids
